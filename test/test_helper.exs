Code.require_file("test/support/setup.ex")

# Schema cache is now populated explicitly as needed during tests
IO.puts("Schema cache simplified - no longer pre-populating during test setup")

Code.require_file("support/test_config.ex", __DIR__)
Code.require_file("support/doctest_case.ex", __DIR__)
Code.require_file("support/data_case.ex", __DIR__)
Code.require_file("support/contract_case.ex", __DIR__)
Code.require_file("support/operation_case.ex", __DIR__)
Code.require_file("support/relation_case.ex", __DIR__)
