defmodule Drops.Relation.SchemaCachePersistenceTest do
  use ExUnit.Case, async: false

  alias Drops.Relation.SchemaCache
  alias Drops.Config

  # Mock repository for testing
  defmodule TestRepo do
    def config do
      [priv: "test/fixtures/persistence_repo"]
    end
  end

  describe "DETS persistence" do
    test "cache persists across GenServer restarts" do
      # Enable cache for tests
      original_config = Config.schema_cache()

      on_exit(fn ->
        Config.update(:schema_cache, original_config)
        File.rm_rf!("test/fixtures")
      end)

      Config.update(:schema_cache, enabled: true)

      # Create test fixture directories
      File.mkdir_p!("test/fixtures/persistence_repo/migrations")

      File.write!(
        "test/fixtures/persistence_repo/migrations/001_create_users.exs",
        "# migration 1"
      )

      # Clear cache to start fresh
      SchemaCache.clear_all()

      # Add an entry to cache
      test_schema = :mock_drops_schema
      SchemaCache.cache_schema(TestRepo, "users", test_schema)

      # Verify it's cached
      result1 = SchemaCache.get_cached_schema(TestRepo, "users")
      assert result1 == test_schema

      # Stop the GenServer to simulate application restart
      GenServer.stop(SchemaCache, :normal)

      # Wait a bit for the process to fully stop
      Process.sleep(100)

      # Start it again (handle case where it might already be restarted by supervisor)
      case SchemaCache.start_link([]) do
        {:ok, _pid} -> :ok
        {:error, {:already_started, _pid}} -> :ok
      end

      # Wait for initialization
      Process.sleep(100)

      # The cache should still contain our entry (persistence test)
      result2 = SchemaCache.get_cached_schema(TestRepo, "users")

      # If persistence works, we should get the original cached value
      assert result2 == test_schema

      # Clean up
      SchemaCache.clear_all()
    end

    test "cache handles DETS file corruption gracefully" do
      # This test ensures the system doesn't crash if DETS file is corrupted
      original_config = Config.schema_cache()

      on_exit(fn ->
        Config.update(:schema_cache, original_config)
      end)

      Config.update(:schema_cache, enabled: true)

      # The system should start successfully even if there are DETS issues
      # (it will fall back to ETS in case of DETS problems)
      assert Process.whereis(SchemaCache) != nil

      # Basic functionality should still work
      test_schema = :fallback_drops_schema
      SchemaCache.cache_schema(TestRepo, "test_table", test_schema)

      result = SchemaCache.get_cached_schema(TestRepo, "test_table")
      assert result == test_schema
    end
  end
end
