defmodule Drops.Relation.SQL.InferenceBinaryIdTest do
  use ExUnit.Case, async: true

  alias Drops.Relation.SQL.Inference

  describe "binary_id field inference" do
    test "normalize_ecto_type handles binary_id correctly" do
      assert Inference.normalize_ecto_type(:binary_id) == :binary
      assert Inference.normalize_ecto_type(:id) == :integer
      assert Inference.normalize_ecto_type(:string) == :string
      assert Inference.normalize_ecto_type({:array, :binary_id}) == {:array, :binary}
    end

    test "field creation logic handles binary_id correctly" do
      # Test the logic from create_field_from_column for binary_id primary key
      id_column = %{name: "id", type: "uuid", primary_key: true, not_null: true}
      base_ecto_type_id = :binary_id

      ecto_type_id =
        cond do
          id_column.primary_key and base_ecto_type_id == :integer ->
            :id

          id_column.primary_key and base_ecto_type_id == :binary_id ->
            :binary_id

          String.ends_with?(id_column.name, "_id") and base_ecto_type_id == :integer ->
            :id

          String.ends_with?(id_column.name, "_id") and base_ecto_type_id == :binary_id ->
            :binary_id

          true ->
            base_ecto_type_id
        end

      assert ecto_type_id == :binary_id
      assert Inference.normalize_ecto_type(ecto_type_id) == :binary
    end

    test "field creation logic handles binary_id foreign keys correctly" do
      # Test the logic from create_field_from_column for binary_id foreign key
      user_id_column = %{
        name: "user_id",
        type: "uuid",
        primary_key: false,
        not_null: true
      }

      base_ecto_type_user_id = :binary_id

      ecto_type_user_id =
        cond do
          user_id_column.primary_key and base_ecto_type_user_id == :integer ->
            :id

          user_id_column.primary_key and base_ecto_type_user_id == :binary_id ->
            :binary_id

          String.ends_with?(user_id_column.name, "_id") and
              base_ecto_type_user_id == :integer ->
            :id

          String.ends_with?(user_id_column.name, "_id") and
              base_ecto_type_user_id == :binary_id ->
            :binary_id

          true ->
            base_ecto_type_user_id
        end

      assert ecto_type_user_id == :binary_id
      assert Inference.normalize_ecto_type(ecto_type_user_id) == :binary
    end
  end
end
