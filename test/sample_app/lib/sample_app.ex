defmodule SampleApp do
  @moduledoc """
  Sample application for testing Drops.Relation schema generation.
  """

  use Application

  def start(_type, _args) do
    children = [
      SampleApp.Repo
    ]

    opts = [strategy: :one_for_one, name: SampleApp.Supervisor]

    {:ok, _} = Application.ensure_all_started(:drops)
    {:ok, _} = Application.ensure_all_started(:ecto_sql)

    pid = Supervisor.start_link(children, opts)

    case Drops.Relation.Cache.refresh(SampleApp.Repo) do
      :ok ->
        IO.puts("✓ Schema cache populated")

      {:error, error} ->
        IO.puts("⚠ Failed to populate schema cache: #{inspect(error)}")

      :cache_disabled ->
        IO.puts("- Schema cache disabled")
    end

    pid
  end
end
